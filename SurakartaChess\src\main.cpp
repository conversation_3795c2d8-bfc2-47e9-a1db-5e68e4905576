#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "../include/game.h"
#include "../include/config.h"

// 函数声明（实现在game.cpp中）
extern int Board[6][6]; // 棋盘结构
extern int computerSide; // 己方执棋颜色
extern int start_flag; // 对局开始标记

int main() {
    Step step; // 临时步结构
    char message[256]; // 通信消息缓冲

    // 初始化随机数种子
    srand((unsigned int)time(NULL));

    // 初始化代码
    init_board();

    // 程序主循环
    while (1) {
        fflush(stdout);

        // 获取平台消息
        if (scanf("%s", message) != 1) {
            // 输入结束或出错，退出程序
            break;
        }

        // 分析命令
        if (strcmp(message, "move") == 0) { // 行棋
            scanf("%s", message); // 获取对手行棋着法
            fflush(stdin);

            // 解析坐标：message[0]=列(X), message[1]=行(Y)
            int start_col = message[0] - 'A';  // 起始列 (0-5)
            int start_row = message[1] - 'A';  // 起始行 (0-5)
            int end_col = message[2] - 'A';    // 目标列 (0-5)
            int end_row = message[3] - 'A';    // 目标行 (0-5)

            // 验证坐标范围
            if (start_col < 0 || start_col >= 6 || start_row < 0 || start_row >= 6 ||
                end_col < 0 || end_col >= 6 || end_row < 0 || end_row >= 6) {
                // 坐标超出范围，跳过这次移动
                continue;
            }

            // 处理对手移动（只更新棋盘状态，不控制对手棋子）
            process_opponent_move(start_col, start_row, end_col, end_row);

            // 生成己方着法
            generate_ai_move(&step);

            // 执行己方移动（更新棋盘和容器）
            execute_move(step.start.y, step.start.x, step.end.y, step.end.x, computerSide);

            // 输出着法 - 格式：列行列行
            printf("move %c%c%c%c\n", step.start.x + 'A', step.start.y + 'A',
                   step.end.x + 'A', step.end.y + 'A');
        }
        else if (strcmp(message, "new") == 0) { // 建立新棋局
            int i, j;

            scanf("%s", message); // 获取己方执棋颜色
            fflush(stdin);

            if (strcmp(message, "black") == 0) // 执黑
                computerSide = BLACK;
            else // 执白
                computerSide = WHITE;

            // 初始化棋局 - 标准苏拉卡尔塔棋布局
            // 上方两行是BLACK棋子
            for (i = 0; i < 2; ++i)
                for (j = 0; j < 6; ++j)
                    Board[i][j] = BLACK;
            // 中间两行为空
            for (i = 2; i < 4; ++i)
                for (j = 0; j < 6; ++j)
                    Board[i][j] = EMPTY;
            // 下方两行是WHITE棋子
            for (i = 4; i < 6; ++i)
                for (j = 0; j < 6; ++j)
                    Board[i][j] = WHITE;

            start_flag = 1;

            if (computerSide == BLACK) {
                // 生成第一手着法
                generate_ai_move(&step);

                // 执行己方移动（更新棋盘和容器）
                execute_move(step.start.y, step.start.x, step.end.y, step.end.x, computerSide);

                // 输出着法
                printf("move %c%c%c%c\n", step.start.x + 'A', step.start.y + 'A',
                       step.end.x + 'A', step.end.y + 'A');
            }
        }
        else if (strcmp(message, "error") == 0) { // 着法错误
            fflush(stdin);

            // 重新生成着法
            generate_ai_move(&step);

            // 执行己方移动（更新棋盘和容器）
            execute_move(step.start.y, step.start.x, step.end.y, step.end.x, computerSide);

            // 输出着法
            printf("move %c%c%c%c\n", step.start.x + 'A', step.start.y + 'A',
                   step.end.x + 'A', step.end.y + 'A');
        }
        else if (strcmp(message, "name?") == 0) { // 询问引擎名
            fflush(stdin);

            // 输出引擎名
            printf("name SurakartaEngine\n");
        }
        else if (strcmp(message, "end") == 0) { // 对局结束
            fflush(stdin);

            start_flag = 0;
        }
        else if (strcmp(message, "quit") == 0) { // 退出引擎
            fflush(stdin);
            break;
        }
    }
    return 0;
}

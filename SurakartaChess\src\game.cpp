#include <stdio.h>
#include <stdbool.h>
#include <stdlib.h>
#include <math.h>
#include <time.h>
#include <string.h>
#include <vector>
#include "../include/game.h"
#include "../include/config.h"

// 棋子位置结构
typedef struct {
    int row, col;
} Position;

// 全局变量定义
int Board[6][6]; // 棋盘结构
int computerSide; // 己方执棋颜色
int start_flag = 0; // 对局开始标记
int T = -1; // 兼容性变量（不再使用）

// 使用容器管理棋子位置
std::vector<Position> blackPieces;
std::vector<Position> whitePieces;

// 苏拉卡尔塔棋规则检查函数
bool is_valid_position(int row, int col) {
    return row >= 0 && row < 6 && col >= 0 && col < 6;
}

// 检查是否为相邻移动（8个方向）
bool is_adjacent_move(int from_row, int from_col, int to_row, int to_col) {
    int dr = abs(to_row - from_row);
    int dc = abs(to_col - from_col);
    return (dr <= 1 && dc <= 1) && (dr + dc > 0);
}

// 检查弧线路径吃子（苏拉卡尔塔棋特殊规则）
bool can_capture_via_arc(int from_row, int from_col, int to_row, int to_col) {
    // 简化实现：只有在棋盘边缘的棋子才能进行弧线吃子
    bool from_on_edge = (from_row == 0 || from_row == 5 || from_col == 0 || from_col == 5);
    bool to_on_edge = (to_row == 0 || to_row == 5 || to_col == 0 || to_col == 5);

    if (!from_on_edge || !to_on_edge) return false;

    // 检查是否不相邻（弧线移动必须跨越距离）
    int dr = abs(to_row - from_row);
    int dc = abs(to_col - from_col);
    return (dr > 1 || dc > 1);
}

// 初始化棋子容器
void init_pieces() {
    blackPieces.clear();
    whitePieces.clear();

    // 初始化黑方棋子（上方两行）
    for (int row = 0; row < 2; row++) {
        for (int col = 0; col < 6; col++) {
            blackPieces.push_back({row, col});
        }
    }

    // 初始化白方棋子（下方两行）
    for (int row = 4; row < 6; row++) {
        for (int col = 0; col < 6; col++) {
            whitePieces.push_back({row, col});
        }
    }
}

// 从容器中移除棋子
void remove_piece(int row, int col, int piece_color) {
    if (piece_color == BLACK) {
        for (auto it = blackPieces.begin(); it != blackPieces.end(); ++it) {
            if (it->row == row && it->col == col) {
                blackPieces.erase(it);
                break;
            }
        }
    } else if (piece_color == WHITE) {
        for (auto it = whitePieces.begin(); it != whitePieces.end(); ++it) {
            if (it->row == row && it->col == col) {
                whitePieces.erase(it);
                break;
            }
        }
    }
}

// 更新棋子位置
void update_piece_position(int from_row, int from_col, int to_row, int to_col, int piece_color) {
    if (piece_color == BLACK) {
        for (auto& piece : blackPieces) {
            if (piece.row == from_row && piece.col == from_col) {
                piece.row = to_row;
                piece.col = to_col;
                break;
            }
        }
    } else if (piece_color == WHITE) {
        for (auto& piece : whitePieces) {
            if (piece.row == from_row && piece.col == from_col) {
                piece.row = to_row;
                piece.col = to_col;
                break;
            }
        }
    }
}

// 同步容器与棋盘状态（确保一致性）
void sync_pieces_with_board() {
    blackPieces.clear();
    whitePieces.clear();

    // 重新扫描棋盘，更新容器
    for (int row = 0; row < 6; row++) {
        for (int col = 0; col < 6; col++) {
            if (Board[row][col] == BLACK) {
                blackPieces.push_back({row, col});
            } else if (Board[row][col] == WHITE) {
                whitePieces.push_back({row, col});
            }
        }
    }
}

// 严格的移动合法性检查
bool is_valid_move_strict(int from_row, int from_col, int to_row, int to_col, int player) {
    // 1. 检查坐标范围
    if (!is_valid_position(from_row, from_col) || !is_valid_position(to_row, to_col)) {
        return false;
    }

    // 2. 检查起始位置是否有己方棋子
    if (Board[from_row][from_col] != player) {
        return false;
    }

    // 3. 检查目标位置不能是己方棋子
    if (Board[to_row][to_col] == player) {
        return false;
    }

    // 4. 如果目标位置为空，只能相邻移动
    if (Board[to_row][to_col] == EMPTY) {
        return is_adjacent_move(from_row, from_col, to_row, to_col);
    }

    // 5. 如果目标位置有对方棋子，检查吃子规则
    int opponent = (player == BLACK) ? WHITE : BLACK;
    if (Board[to_row][to_col] == opponent) {
        // 相邻吃子或弧线吃子
        return is_adjacent_move(from_row, from_col, to_row, to_col) ||
               can_capture_via_arc(from_row, from_col, to_row, to_col);
    }

    return false;
}

// 执行移动（更新棋盘和容器）
void execute_move(int from_row, int from_col, int to_row, int to_col, int player) {
    // 如果目标位置有对方棋子，先移除它
    if (Board[to_row][to_col] != EMPTY) {
        remove_piece(to_row, to_col, Board[to_row][to_col]);
    }

    // 更新棋盘
    Board[to_row][to_col] = Board[from_row][from_col];
    Board[from_row][from_col] = EMPTY;

    // 更新容器中的棋子位置
    update_piece_position(from_row, from_col, to_row, to_col, player);
}

// 新的AI着法生成（只控制己方棋子）
void generate_ai_move(Step* step) {
    int best_score = -10000;
    bool found = false;
    Step best_move;

    // 同步容器与棋盘状态，确保我们有最新的棋子位置
    sync_pieces_with_board();

    // 获取己方棋子容器
    std::vector<Position>* my_pieces = (computerSide == BLACK) ? &blackPieces : &whitePieces;
    int opponent = (computerSide == BLACK) ? WHITE : BLACK;

    // 遍历己方所有棋子
    for (const auto& piece : *my_pieces) {
        int from_row = piece.row;
        int from_col = piece.col;

        // 尝试8个方向的移动
        int directions[8][2] = {{-1,-1}, {-1,0}, {-1,1}, {0,-1}, {0,1}, {1,-1}, {1,0}, {1,1}};

        for (int d = 0; d < 8; d++) {
            int to_row = from_row + directions[d][0];
            int to_col = from_col + directions[d][1];

            // 检查移动是否合法
            if (is_valid_move_strict(from_row, from_col, to_row, to_col, computerSide)) {
                // 评估移动
                int score = 0;

                // 吃子奖励（最高优先级）
                if (Board[to_row][to_col] == opponent) {
                    score += 1000;
                }

                // 前进奖励
                if (computerSide == BLACK) {
                    score += (to_row - from_row) * 50; // 黑方向下前进
                } else {
                    score += (from_row - to_row) * 50; // 白方向上前进
                }

                // 中心位置奖励
                int center_dist = abs(to_row - 2) + abs(to_col - 2);
                score += (6 - center_dist) * 10;

                // 随机因子
                score += rand() % 20;

                if (score > best_score || !found) {
                    best_score = score;
                    best_move.start.x = from_col; // x = 列
                    best_move.start.y = from_row; // y = 行
                    best_move.end.x = to_col;
                    best_move.end.y = to_row;
                    found = true;
                }
            }
        }
    }

    if (found) {
        *step = best_move;
    } else {
        // 如果没找到合法移动，选择第一个可用的棋子进行简单移动
        if (!my_pieces->empty()) {
            Position first_piece = (*my_pieces)[0];
            step->start.x = first_piece.col;
            step->start.y = first_piece.row;
            step->end.x = first_piece.col;
            step->end.y = first_piece.row + ((computerSide == BLACK) ? 1 : -1);
        }
    }
}

// 通信协议相关函数实现

// 初始化棋盘
void init_board() {
    // 清空棋盘
    for (int i = 0; i < 6; i++) {
        for (int j = 0; j < 6; j++) {
            Board[i][j] = EMPTY;
        }
    }

    // 初始化标准苏拉卡尔塔棋局
    for (int i = 0; i < 2; i++) {
        for (int j = 0; j < 6; j++) {
            Board[i][j] = BLACK;
        }
    }
    for (int i = 4; i < 6; i++) {
        for (int j = 0; j < 6; j++) {
            Board[i][j] = WHITE;
        }
    }

    // 初始化棋子容器
    init_pieces();
}

// 处理对手移动（更新棋盘状态和对手棋子位置以避免冲突）
void process_opponent_move(int from_col, int from_row, int to_col, int to_row) {
    // 验证坐标范围
    if (!is_valid_position(from_row, from_col) || !is_valid_position(to_row, to_col)) {
        return;
    }

    // 获取移动的棋子
    int piece = Board[from_row][from_col];
    if (piece == EMPTY) return;

    // 如果目标位置有己方棋子，从己方容器中移除（被对手吃掉）
    if (Board[to_row][to_col] != EMPTY && Board[to_row][to_col] == computerSide) {
        remove_piece(to_row, to_col, computerSide);
    }

    // 更新棋盘状态
    Board[to_row][to_col] = piece;
    Board[from_row][from_col] = EMPTY;

    // 更新对手棋子在容器中的位置（为了避免冲突，我们需要跟踪对手棋子位置）
    // 虽然我们不控制对手的移动决策，但需要知道它们在哪里
    int opponent = (computerSide == BLACK) ? WHITE : BLACK;
    if (piece == opponent) {
        update_piece_position(from_row, from_col, to_row, to_col, opponent);
    }
}

// 坐标转换函数
Point coord_to_point(char x, char y) {
    Point p;
    p.x = x - 'A';
    p.y = y - 'A';
    return p;
}

void point_to_coord(Point p, char* x, char* y) {
    *x = p.x + 'A';
    *y = p.y + 'A';
}

// 统计某个玩家的棋子数量（兼容性函数）
int count_pieces(int board[6][6], int player) {
    int count = 0;
    for (int i = 0; i < 6; i++) {
        for (int j = 0; j < 6; j++) {
            if (board[i][j] == player) {
                count++;
            }
        }
    }
    return count;
}

// 简化的移动合法性检查（保持兼容性）
int is_valid_engine_move(int from_x, int from_y, int to_x, int to_y, int player) {
    return is_valid_move_strict(from_y, from_x, to_y, to_x, player);
}

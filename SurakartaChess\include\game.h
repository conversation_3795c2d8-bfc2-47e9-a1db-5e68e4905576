#ifndef GAME_H
#define GAME_H

#include <stdbool.h>
#include "config.h"

// 通信协议相关结构体
typedef struct _Point {
    int x, y;
} Point;

typedef struct _Step {
    Point start, end;
    int value;
} Step;

// 全局变量声明
extern int Board[6][6]; // 棋盘结构
extern int computerSide; // 己方执棋颜色
extern int start_flag; // 对局开始标记

// 通信协议相关函数
void init_board();
void generate_ai_move(Step* step);
int is_valid_engine_move(int from_x, int from_y, int to_x, int to_y, int player);
void process_opponent_move(int from_col, int from_row, int to_col, int to_row);
void execute_move(int from_row, int from_col, int to_row, int to_col, int player);
void sync_pieces_with_board();

// 坐标转换函数
Point coord_to_point(char x, char y);
void point_to_coord(Point p, char* x, char* y);

#endif // GAME_H

#ifndef UTILS_H
#define UTILS_H

#include <stdbool.h>
#include "config.h"

// 数组操作工具函数
int find_max(int arr[], int size);
int find_min(int arr[], int size);
int find_max_index(int arr[], int size);
int find_max2(int arr[], int size);
int find_min2(int arr[], int size);

// 游戏状态检查
int count_pieces(int board[BOARD_SIZE][BOARD_SIZE], int player);
int calculate_mobility(int board[BOARD_SIZE][BOARD_SIZE], int player);
bool is_game_over(int board[BOARD_SIZE][BOARD_SIZE]);

#endif // UTILS_H

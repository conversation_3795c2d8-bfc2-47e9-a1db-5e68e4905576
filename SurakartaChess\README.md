# Surakarta Chess Engine (SAU Game Platform Compatible)

A smart AI engine for the Surakarta board game, completely rewritten to follow the SAU Game Platform communication protocol as specified in "通信协议说明与引擎编写规范.txt".

## 🎯 Project Overview

This project has been **completely rewritten** from the original interactive game to a communication protocol-compliant engine that can be used with the SAU Game Platform. The engine follows the exact specifications outlined in the communication protocol documentation.

## ✨ Key Features

- **SAU Protocol Compliant**: Fully implements the communication protocol for Surakarta chess engines
- **Standard I/O Communication**: Uses stdin/stdout for platform communication
- **Advanced AI**: Implements minimax algorithm with intelligent move evaluation
- **Proper Command Handling**: Supports all required commands (name?, new, move, error, end, quit)
- **Coordinate System**: Uses A-F letter coordinates as specified in the protocol
- **Robust Error Handling**: Graceful handling of invalid inputs and edge cases

## 🎮 Supported Commands

The engine responds to the following SAU Game Platform commands:

| Command | Description | Response |
|---------|-------------|----------|
| `name?` | Query engine name | `name SurakartaEngine` |
| `new black` | Start new game as black (first player) | Outputs first move |
| `new white` | Start new game as white (second player) | Waits for opponent move |
| `move XXYY` | Opponent's move from XX to YY | Responds with engine's move |
| `error` | Previous move was invalid | Generates new move |
| `end` | Game over | Acknowledges end |
| `quit` | Terminate engine | `Quit!` and exit |

## 🏗️ Building

### Prerequisites
- GCC compiler with C++11 support
- Windows environment (for batch scripts)

### Compilation

```bash
.\build.bat
```

This creates the protocol-compliant executable `bin/surakarta_engine.exe`.

## 🚀 Usage

### With SAU Game Platform
The engine is designed to be loaded by the SAU Game Platform. The platform will handle all communication automatically.

### Manual Testing
For testing purposes, you can run the engine manually:

```bash
# Test basic functionality
echo name? | bin\surakarta_engine.exe

# Test game sequence
echo new black | bin\surakarta_engine.exe

# Run test scripts
.\test_protocol.bat
.\test_game.bat
```

## 📁 Project Structure

```
├── src/                    # Source files
│   ├── main.cpp           # Protocol communication handler
│   ├── game.cpp           # Game logic and board management
│   ├── ai.cpp             # AI algorithms and move generation
│   ├── board.cpp          # Board representation utilities
│   └── utils.cpp          # Helper functions
├── include/               # Header files
│   ├── game.h             # Game logic declarations
│   ├── ai.h               # AI function declarations
│   ├── config.h           # Configuration constants
│   ├── board.h            # Board utilities
│   └── utils.h            # Utility declarations
├── obj/                   # Compiled object files
├── bin/                   # Executable files
│   └── surakarta_engine.exe  # Protocol-compliant engine
├── build.bat              # Build script
├── test_protocol.bat      # Protocol testing script
├── test_game.bat          # Game sequence testing script
└── 通信协议说明与引擎编写规范.txt  # Protocol specification
```

## 🎯 Game Rules (Surakarta)

- **Board**: 6×6 grid
- **Setup**: Black pieces on rows 0-1, White pieces on rows 4-5
- **Movement**: Adjacent moves or captures along circular arc paths
- **Victory**: Reduce opponent to 1 or fewer pieces
- **Coordinates**: A-F (columns) × A-F (rows), e.g., AA = (0,0), FF = (5,5)

## 🤖 AI Algorithm

The engine implements:

- **Minimax with Alpha-Beta Pruning**: Efficient game tree search
- **Position Evaluation**: Strategic piece positioning and mobility
- **Capture Priority**: Prioritizes capturing opponent pieces
- **Endgame Optimization**: Special handling for critical game phases
- **Random Factor**: Prevents overly predictable play

## 🧪 Testing

The project includes comprehensive testing:

```bash
# Test all protocol commands
.\test_protocol.bat

# Test complete game sequence
.\test_game.bat
```

Expected outputs:

- `name?` → `name SurakartaEngine`
- `new black` → `move XXYY` (engine's first move)
- `new white` → (waits for opponent)
- `quit` → `Quit!`

## 📋 Protocol Compliance

This engine strictly follows the SAU Game Platform communication protocol:

- ✅ Standard input/output communication
- ✅ Proper command parsing and response
- ✅ Coordinate system (A-F letters)
- ✅ Move format (XXYY for start and end positions)
- ✅ Buffer flushing as required
- ✅ Graceful error handling

## 🔄 Migration Notes

**Major Changes from Original Version:**

1. **Complete rewrite** of main.cpp for protocol compliance
2. **Removed interactive UI** - now uses stdin/stdout only
3. **Added protocol command handlers** for all required commands
4. **Coordinate system change** from numeric to letter-based (A-F)
5. **AI integration** adapted for protocol communication
6. **Board representation** updated for protocol requirements

## 📄 License

This project is open source and available under the MIT License.

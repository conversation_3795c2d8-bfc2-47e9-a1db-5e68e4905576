#ifndef BOARD_H
#define BOARD_H

#include <stdbool.h>
#include "config.h"

// 棋盘操作相关函数
void print_board(int board[BOARD_SIZE][BOARD_SIZE]);
void copy_board(int src[BOARD_SIZE][BOARD_SIZE], int dest[BOARD_SIZE][BOARD_SIZE]);
void make_move(int x, int y, int new_x, int new_y, int board[BOARD_SIZE][BOARD_SIZE]);
bool is_valid_position(int x, int y);
bool is_empty(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);

// 苏拉卡尔塔棋特殊移动规则
int check_capture_direction1(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);
int check_capture_direction2(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);
int check_capture_direction3(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);
int check_capture_direction4(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);

void execute_capture1(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);
void execute_capture2(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);
void execute_capture3(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);
void execute_capture4(int board[BOARD_SIZE][BOARD_SIZE], int x, int y);

// 辅助函数
int check_circular_capture(int board[BOARD_SIZE][BOARD_SIZE], int x, int y, int path[][2], int path_len);
void execute_circular_capture(int board[BOARD_SIZE][BOARD_SIZE], int x, int y, int direction);

#endif // BOARD_H

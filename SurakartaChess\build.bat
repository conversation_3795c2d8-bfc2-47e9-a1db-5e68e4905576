@echo off
echo Building Surakarta Chess Engine (Communication Protocol)...

REM Create directories
if not exist obj mkdir obj
if not exist bin mkdir bin

REM Compile source files
echo Compiling main.cpp...
g++ -Wall -Wextra -std=c++11 -O2 -Iinclude -c src/main.cpp -o obj/main.o
if errorlevel 1 goto error

echo Compiling game.cpp...
g++ -Wall -Wextra -std=c++11 -O2 -Iinclude -c src/game.cpp -o obj/game.o
if errorlevel 1 goto error

echo Compiling board.cpp...
g++ -Wall -Wextra -std=c++11 -O2 -Iinclude -c src/board.cpp -o obj/board.o
if errorlevel 1 goto error

echo Compiling utils.cpp...
g++ -Wall -Wextra -std=c++11 -O2 -Iinclude -c src/utils.cpp -o obj/utils.o
if errorlevel 1 goto error

echo Compiling ai.cpp...
g++ -Wall -Wextra -std=c++11 -O2 -Iinclude -c src/ai.cpp -o obj/ai.o
if errorlevel 1 goto error

REM Link object files
echo Linking...
g++ obj/main.o obj/game.o obj/board.o obj/utils.o obj/ai.o -o bin/surakarta_engine_fixed.exe
if errorlevel 1 goto error

echo Build complete!
echo Engine executable: bin\surakarta_engine_fixed.exe
echo This engine follows SAU Game Platform communication protocol.

goto end

:error
echo Build failed!
echo.
pause

:end
echo.
pause
